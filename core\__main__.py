import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from state_mgmt.StateManager import StateManager

def test_state_output_success():
    return StateOutput(
        status=StatusType.SUCCESS,
        message="Operation successful",
        code=StatusCode.OK,
        outputs={"data": "test"},
        meta={"source": "test_suite"}
    )


if __name__ == "__main__":
    sm = StateManager("GenericBank.json", "test_session")

    state = sm.get_state("state_check_balance")
    print("Loaded State:", state.id)
    print(state)
    

    layer2 = sm.get_layer2("l2_check_balance")
    print("Loaded Layer2:", layer2.id)
    print(layer2)

    print(test_state_output_success())

    # --- Execute the entire workflow step by step ---
    print("\n--- Executing Workflow ---")
    sm.is_running = True
    step_count = 0
    while sm.is_running:
        print(f"\nStep {step_count} (Current State: {sm.current_state_id})")
        output = sm.execute_step({})
        print("Step Output:", output)
        print(f"Next State: {sm.current_state_id}")
        step_count += 1
    print("\nWorkflow execution complete.")





