import json
from typing import Optional
from pydantic import ValidationError

from .states.Layer2 import Layer2  # Corrected relative import


class Layer2SchemaLoader:
    @staticmethod
    def load(file_path: str) -> Optional[Layer2]:
        """
        Loads a Layer 2 schema JSON file and parses it into a Layer2 object.

        Args:
            file_path (str): Path to the Layer 2 JSON file.

        Returns:
            Layer2: Parsed Layer2 object, or None if there's an error.
        """
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                layer2 = Layer2(**data)
                return layer2
        except FileNotFoundError:
            print(f"❌ Error: File not found - {file_path}")
        except json.JSONDecodeError as e:
            print(f"❌ Error: Invalid JSON format - {e}")
        except ValidationError as e:
            print(f"❌ Error: JSON does not match the expected schema:\n{e}")
        return None
