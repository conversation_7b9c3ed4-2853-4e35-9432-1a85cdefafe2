import os
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone

from .WorkflowSchemaLoader import WorkflowSchemaLoader
from .Layer2SchemaLoader import Layer2SchemaLoader
from .states.Workflow import WorkflowWrapper,State
from .states.Layer2 import Layer2, PipelineStep
from schemas.outputSchema import StateOutput, StatusType, StatusCode

# TODO: Implement full MemoryManager logic for all memory operations
class MemoryManager:
    def __init__(self):
        pass
    # TODO: Add methods for memory layers and session context management

class StateManager:
    """
    Main State Manager - the brain of the agent's execution loop.
    
    This class manages:
    - Workflow state transitions based on the provided schema format
    - Execution of Layer2 pipelines
    - Memory management (TODO: integrate with MemoryManager)
    - Agent coordination (TODO: integrate with MCPAgentRegistry)
    """
    
    def __init__(self, workflow_name: str, session_id: str ,user_id: Optional[str] = None):
        """
        Initialize the State Manager.
        
        Args:
            workflow_name: Name of the workflow to execute
            session_id: Unique session identifier
            user_id: Optional user identifier
        """
        self.workflow_name = workflow_name
        self.user_id = user_id
        self.session_id = session_id
        self.workflow_file_path = os.path.join("core", "state_mgmt", "states", "workflow_states", workflow_name)
        self.workflow: WorkflowWrapper = None
        self.layer2_map: Dict[str, Layer2] = {}
        # MemoryManager placeholder (to be implemented)
        self.memory_manager = MemoryManager()  # TODO: Replace all direct memory usage with MemoryManager
        self.session_context: Dict[str, Any] = {}
        self.execution_history = []
        self.is_running = True
        self.current_state_id = None
        self.contextual_memory = {}  # Add back a simple dict to avoid errors

        # TODO: Replace with proper MemoryManager integration
        # Memory layers (simplified for now - will be replaced with MemoryManager)
        # self.ephemeral_memory: Dict[str, Any] = {}
        # self.contextual_memory: Dict[str, Any] = {}
        # self.persistent_memory: Dict[str, Any] = {}
        
        # TODO: Replace with proper MemoryManager.get_session_context()
        # Session context for passing data between states
        # self.session_context: Dict[str, Any] = {}  # Will be managed by MemoryManager in future
        
        # TODO: Add MCPAgentRegistry integration
        # self.agent_registry = MCPAgentRegistry()
        

        # Load workflow and Layer2 schemas
        try:
            self._load_workflow()
            self._load_all_layer2()
            # Set initial state
            if self.workflow and hasattr(self.workflow.workflow, 'start'):
                self.current_state_id = self.workflow.workflow.start
        except Exception as e:
            print(f"Error initializing StateManager: {e}")
            self.workflow = None

        self.current_state_id: str = self.workflow.workflow.start
        self.current_layer2_id_step: str = self.layer2_map.get(self.get_state(self.current_state_id).layer2_id).pipeline[0].step
    
    def _load_workflow(self):
        try:
            self.workflow = WorkflowSchemaLoader.load(self.workflow_file_path)
            if not self.workflow:
                raise Exception(f"Failed to load workflow: {self.workflow_file_path}")
        except Exception as e:
            print(f"Error loading workflow: {e}")
            self.workflow = None

    def _load_all_layer2(self):
        if not self.workflow or not hasattr(self.workflow, 'workflow'):
            print("No workflow loaded, cannot load Layer2 schemas.")
            return
        for state in self.workflow.workflow.states.values():
            layer2_id = getattr(state, 'layer2_id', None)
            if not layer2_id:
                continue
            layer2_file = os.path.join("core", "state_mgmt", "states", "execution_states", f"{layer2_id}.json")
            if not os.path.isfile(layer2_file):
                print(f"⚠️ Warning: Layer2 file not found - {layer2_file}")
                continue
            try:
                layer2_obj = Layer2SchemaLoader.load(layer2_file)
                if layer2_obj:
                    self.layer2_map[layer2_id] = layer2_obj
                else:
                    print(f"⚠️ Warning: Failed to load Layer2 from {layer2_file}")
            except Exception as e:
                print(f"Error loading Layer2 {layer2_id}: {e}")

    def get_state(self, state_id: str):
        return self.workflow.workflow.states.get(state_id)

    def get_layer2(self, layer2_id: str):
        return self.layer2_map.get(layer2_id)
    
    def execute_step(self, input_data: Optional[Dict[str, Any]] = None) -> StateOutput:
        """
        Execute a single step in the workflow.
        
        Args:
            input_data: Input data for the current step
            
        Returns:
            StateOutput: Result of the step execution
        """
        if not self.workflow:
            return StateOutput(
                status=StatusType.ERROR,
                message="No workflow loaded",
                code=StatusCode.BAD_REQUEST,
                outputs={},
                meta={"error": "workflow_not_loaded"}
            )
        
        if not self.current_state_id:
            return StateOutput(
                status=StatusType.ERROR,
                message="No current state",
                code=StatusCode.BAD_REQUEST,
                outputs={},
                meta={"error": "no_current_state"}
            )
        
        try:
            # Get current state
            current_state = self.get_state(self.current_state_id)
            
            if not current_state:
                return StateOutput(
                    status=StatusType.ERROR,
                    message=f"State '{self.current_state_id}' not found",
                    code=StatusCode.NOT_FOUND,
                    outputs={},
                    meta={"current_state": self.current_state_id}
                )
            
            print(f"Executing state: {current_state.id} (type: {current_state.type})")
            
            # TODO: Use MemoryManager.update_memory() instead of direct assignment
            # Update session context with input data
            if input_data:
                self.session_context.update(input_data)
            
            # Execute Layer2 pipeline for this state
            layer2_id = current_state.layer2_id
            if layer2_id:
                pipeline_result = self._execute_layer2_pipeline(layer2_id, input_data)
            else:
                # No pipeline, create simple success output
                pipeline_result = StateOutput(
                    status=StatusType.SUCCESS,
                    message=f"State '{self.current_state_id}' executed",
                    code=StatusCode.OK,
                    outputs=input_data or {},
                    meta={"state_id": self.current_state_id}
                )
            
            # TODO: Use MemoryManager.set() instead of direct assignment
            # Store result in contextual memory
            self.contextual_memory[f"state_output_{self.current_state_id}"] = pipeline_result.dict()
            
            # Update session context with outputs
            if pipeline_result.outputs:
                self.session_context.update(pipeline_result.outputs)

            # Determine next state based on transitions
            next_state_id = self._determine_next_state(current_state, pipeline_result)

            # Update execution history
            self.execution_history.append({
                "state_id": self.current_state_id,
                "state_name": current_state.id,
                "state_type": current_state.type,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "input": input_data,
                "output": pipeline_result.dict(),
                "next_state": next_state_id
            })

            # Transition to next state
            if next_state_id and next_state_id != self.current_state_id:
                self.current_state_id = next_state_id
                print(f"Transitioning to state: {next_state_id}")
            elif not next_state_id:
                self.is_running = False
                print("Workflow execution completed")
            
            return pipeline_result
            
        except Exception as e:
            print(f"Error executing step: {e}")
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Step execution failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"current_state": self.current_state_id, "error": str(e)}
            )
    
    def _execute_layer2_pipeline(self, layer2_id: str, input_data: Optional[Dict[str, Any]]) -> StateOutput:
        """
        Execute a Layer2 pipeline.
        
        Args:
            layer2_id: ID of the Layer2 pipeline to execute
            input_data: Input data for the pipeline
            
        Returns:
            StateOutput: Result of pipeline execution
        """
        layer2_obj = self.get_layer2(layer2_id)
        
        if not layer2_obj:
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Layer2 pipeline '{layer2_id}' not found",
                code=StatusCode.NOT_FOUND,
                outputs={},
                meta={"layer2_id": layer2_id}
            )
        
        try:
            print(f"Executing Layer2 pipeline: {layer2_id} (version {layer2_obj.version})")
            
            pipeline_outputs = {}
            
            # Execute each step in the pipeline
            for step in layer2_obj.pipeline:
                step_result = self._execute_pipeline_step(step, input_data, pipeline_outputs)
                # TODO: Implement actual retry logic for failed steps
                # Handle step failure
                if step_result.status == StatusType.ERROR:
                    # TODO: Implement proper error handling with retry logic
                    # Check error handling configuration
                    if layer2_obj.onError:
                        retry_count = layer2_obj.onError.retry
                        
                        if retry_count > 0:
                            print(f"Retrying step {step.step} ({retry_count} attempts left)") 
                            # TODO: Implement actual retry logic
                            continue
                        else:
                            fallback_state = layer2_obj.onError.fallback_state
                            if fallback_state:
                                print(f"Using fallback state: {fallback_state}")
                                # Execute fallback pipeline
                                return self._execute_layer2_pipeline(fallback_state, input_data)
                            else:
                                return step_result
                    else:
                        return step_result
                
                # Update pipeline outputs
                pipeline_outputs.update(step_result.outputs)
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message=f"Layer2 pipeline '{layer2_id}' executed successfully",
                code=StatusCode.OK,
                outputs=pipeline_outputs,
                meta={
                    "layer2_id": layer2_id,
                    "steps_executed": len(layer2_obj.pipeline),
                    "execution_time": datetime.now(timezone.utc).isoformat()
                }
            )
            
        except Exception as e:
            print(f"Error executing Layer2 pipeline {layer2_id}: {e}")
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Pipeline execution failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"layer2_id": layer2_id, "error": str(e)}
            )
    
    def _execute_pipeline_step(self, step: PipelineStep, input_data: Optional[Dict[str, Any]], 
                              pipeline_outputs: Dict[str, Any]) -> StateOutput:
        """
        Execute a single pipeline step.
        
        Args:
            step: Pipeline step configuration
            input_data: Input data for the pipeline
            pipeline_outputs: Outputs from previous steps
            
        Returns:
            StateOutput: Result of step execution
        """
        try:
            print(f"Executing pipeline step: {step.step} (Agent: {step.agent})")
            # Remove call to _prepare_agent_input, just merge input_data and pipeline_outputs
            agent_input = {}
            if input_data:
                agent_input.update(input_data)
            if pipeline_outputs:
                agent_input.update(pipeline_outputs)
            
            # TODO: Implement _prepare_agent_input for advanced input mapping/templating
            # TODO: Replace mock agent execution with real agent call via MCPAgentRegistry
            # mock_output = self.agent_registry.execute_agent(step.agent, agent_input, self.session_context)
            mock_output = self._mock_agent_execution(step.agent, agent_input)
            
            # Apply output mapping
            mapped_outputs = {}
            for output_key, mapped_key in step.output.items():
                if output_key in mock_output:
                    mapped_outputs[mapped_key] = mock_output[output_key]
            
            print(f"Step '{step.step}' completed successfully")
            return StateOutput(
                status=StatusType.SUCCESS,
                message=f"Step '{step.step}' executed successfully",
                code=StatusCode.OK,
                outputs=mapped_outputs,
                meta={
                    "step": step.step,
                    "agent": step.agent,
                    "input": agent_input,
                    "tools": step.tools.dict() if step.tools else {}
                }
            )
            
        except Exception as e:
            # TODO: Replace print statements with proper logging
            print(f"Error executing step '{step.step}': {e}")
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Step execution failed: {str(e)}",
                code=StatusCode.AGENT_ERROR,
                outputs={},
                meta={"step": step.step, "error": str(e)}
            )
    
    def _mock_agent_execution(self, agent, agent_input):
        """
        Mock agent execution for testing pipeline steps.
        Returns dummy outputs based on agent and input keys.
        """
        # Special case for intent detection to enable workflow transitions
        if agent == "intent_classifier":
            return {"intent": "check_balance", "slots": {"account_id": "123456"}}
        # For other agents, just echo input keys as outputs with mock values
        return {k: f"mock_{k}" for k in agent_input}
    

    
    def _determine_next_state(self, current_state: State, step_output: StateOutput) -> Optional[str]:
        """
        Determine the next state based on transition rules and step output.
        Returns None if no transition condition matches.
        """
        transitions = getattr(current_state, 'transitions', None) or []

        for transition in transitions:
            # Support both dict and object style for transition
            if isinstance(transition, dict):
                condition = transition.get('condition')
                target = transition.get('target')
            else:
                condition = getattr(transition, 'condition', None)
                target = getattr(transition, 'target', None)
            if self._evaluate_transition_condition(condition):
                return target
        return None

    def _evaluate_transition_condition(self, condition: str) -> bool:
        """Evaluate transition condition (basic implementation)."""
        if not condition or condition == "true":
            return True
        # Simple equality check for demonstration
        if "==" in condition:
            var, val = condition.split("==")
            var = var.strip()
            val = val.strip().strip("'").strip('"')
            actual = self.session_context.get(var)
            return str(actual) == val
        return False
