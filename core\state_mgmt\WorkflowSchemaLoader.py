import json
from typing import Optional
from pydantic import ValidationError

from .states.Workflow import WorkflowWrapper  # Corrected relative import


class WorkflowSchemaLoader:
    @staticmethod
    def load(file_path: str) -> Optional[WorkflowWrapper]:
        """
        Loads a workflow schema JSON file and parses it into a WorkflowWrapper object.

        Args:
            file_path (str): Path to the workflow JSON file.

        Returns:
            WorkflowWrapper: Parsed workflow object, or None if there's an error.
        """
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                workflow = WorkflowWrapper(**data)
                return workflow
        except FileNotFoundError:
            print(f"❌ Error: File not found - {file_path}")
        except json.JSONDecodeError as e:
            print(f"❌ Error: Invalid JSON format - {e}")
        except ValidationError as e:
            print(f"❌ Error: JSON does not match the expected schema:\n{e}")
        return None
