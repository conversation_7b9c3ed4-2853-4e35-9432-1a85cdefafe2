from typing import List, Dict, Optional
from pydantic import BaseModel


class Transition(BaseModel):
    condition: str
    target: str


class State(BaseModel):
    id: str
    type: str
    layer2_id: str
    expected_input: List[str]
    expected_output: List[str]
    transitions: Optional[List[Transition]] = []
    allowed_tools: List[str]


class WorkflowConfig(BaseModel):
    name: str
    version: str
    start: str
    states: Dict[str, State]


class WorkflowWrapper(BaseModel):
    workflow: WorkflowConfig
